import { UserAccountType } from '@constants/user';
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import type { VerifyCallback } from 'passport-google-oauth20';
import { Strategy } from 'passport-google-oauth20';

import { SocialInfoDto } from './dto/social-info.dto.ts';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
	constructor() {
		super({
			clientID: process.env.GOOGLE_CLIENT_ID!,
			clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
			callbackURL: `${process.env.BACKEND_URL}/auth/oauth2/google/callback`,
			scope: ['email', 'profile'],
			passReqToCallback: true,
		});
	}

	authenticate(req: any, options?: any) {
		options = {
			...options,
			state: req.query.verify,
		};
		super.authenticate(req, options);
	}

	async validate(
		req: any,
		accessToken: string,
		refreshToken: string,
		profile: any,
		done: VerifyCallback,
	): Promise<any> {
		const { name, emails, photos } = profile;
		const socialInfo = new SocialInfoDto({
			socialUid: profile.id,
			name: profile.displayName || `${name.givenName} ${name.familyName}`,
			email: emails[0].value,
			avatarUrl: photos[0].value,
			provider: UserAccountType.GOOGLE,
			accessToken,
			refreshToken,
			verifyCode: req.query.state,
		});
		done(null, socialInfo);
	}
}
